# Midjourney Edits API 接口文档

## 接口概述

`/mj/submit/edits` 接口用于提交 Midjourney 编辑任务，支持两种模式：

1. **编辑图片模式**：对图片的指定区域进行编辑
2. **转绘图片模式**：对整张图片进行重新绘制

## 接口地址

```
POST /mj/submit/edits
```

## 请求头

```
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
```

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| prompt | string | 是 | 编辑提示词 |
| image | string | 否 | 原图URL或base64（编辑模式时为选填，转绘模式时必填） |
| maskBase64 | string | 否 | 遮罩图片base64（编辑模式时必填，转绘模式时不需要） |
| notifyHook | string | 否 | 回调地址 |
| state | string | 否 | 自定义状态信息 |
| noStorage | boolean | 否 | 是否不存储结果 |

## 使用模式

### 1. 编辑图片模式

当提供 `maskBase64` 参数时，接口将进入编辑模式。此模式下：
- `prompt`：必填，描述要编辑的内容
- `maskBase64`：必填，遮罩图片的base64编码，需要编辑的区域用透明表示
- `image`：选填，原图的URL或base64编码

**请求示例：**

```json
{
    "prompt": "几只鸟在飞",
    "image": "https://example.com/original.jpg",
    "maskBase64": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
}
```

### 2. 转绘图片模式

当不提供 `maskBase64` 参数时，接口将进入转绘模式。此模式下：
- `prompt`：必填，描述要转绘的风格或内容
- `image`：必填，要转绘的图片URL或base64编码

**请求示例：**

```json
{
    "prompt": "将这张图片转换为油画风格",
    "image": "https://example.com/source.jpg"
}
```

## 响应格式

### 成功响应

```json
{
    "code": 1,
    "description": "Submit Success",
    "result": "1751569585394059"
}
```

### 错误响应

```json
{
    "code": 4,
    "description": "prompt_is_required"
}
```

## 错误码说明

| 错误码 | 描述 | 说明 |
|--------|------|------|
| 4 | prompt_is_required | 缺少必填的 prompt 参数 |
| 4 | image_or_maskBase64_is_required | 既没有提供 image 也没有提供 maskBase64 |

## 注意事项

1. **图片格式**：支持 URL 和 base64 两种格式
2. **遮罩要求**：编辑模式下，遮罩图片中需要编辑的区域必须是透明的
3. **任务类型**：
   - 高级编辑任务类型：`EDIT`
   - 高级重绘任务类型：`RETEXTURE`
4. **操作限制**：高级编辑/高级重绘只能进行放大/重新提交操作

## 完整示例

### 编辑图片示例

```bash
curl -X POST "https://your-api-domain.com/mj/submit/edits" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "在天空中添加几朵白云",
    "image": "https://example.com/landscape.jpg",
    "maskBase64": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
  }'
```

### 转绘图片示例

```bash
curl -X POST "https://your-api-domain.com/mj/submit/edits" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "转换为水彩画风格",
    "image": "https://example.com/photo.jpg"
  }'
```

## 任务查询

提交成功后，可以使用返回的任务ID通过 `/mj/task/{id}/fetch` 接口查询任务状态和结果。

```bash
curl -X GET "https://your-api-domain.com/mj/task/1751569585394059/fetch" \
  -H "Authorization: Bearer YOUR_API_KEY"
```
