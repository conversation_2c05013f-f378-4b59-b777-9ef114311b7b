#!/bin/bash

# 测试 Midjourney Edits API 接口

API_BASE_URL="http://localhost:3000"
API_KEY="your_api_key_here"

echo "=== 测试 Midjourney Edits API ==="

# 测试1: 编辑图片模式（有 maskBase64）
echo "测试1: 编辑图片模式"
curl -X POST "${API_BASE_URL}/mj/submit/edits" \
  -H "Authorization: Bearer ${API_KEY}" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "在天空中添加几朵白云",
    "image": "https://example.com/landscape.jpg",
    "maskBase64": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA4nEKtAAAAABJRU5ErkJggg=="
  }' \
  -w "\nHTTP Status: %{http_code}\n\n"

# 测试2: 转绘图片模式（无 maskBase64）
echo "测试2: 转绘图片模式"
curl -X POST "${API_BASE_URL}/mj/submit/edits" \
  -H "Authorization: Bearer ${API_KEY}" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "转换为水彩画风格",
    "image": "https://example.com/photo.jpg"
  }' \
  -w "\nHTTP Status: %{http_code}\n\n"

# 测试3: 错误情况 - 缺少 prompt
echo "测试3: 错误情况 - 缺少 prompt"
curl -X POST "${API_BASE_URL}/mj/submit/edits" \
  -H "Authorization: Bearer ${API_KEY}" \
  -H "Content-Type: application/json" \
  -d '{
    "image": "https://example.com/photo.jpg"
  }' \
  -w "\nHTTP Status: %{http_code}\n\n"

# 测试4: 错误情况 - 既没有 image 也没有 maskBase64
echo "测试4: 错误情况 - 既没有 image 也没有 maskBase64"
curl -X POST "${API_BASE_URL}/mj/submit/edits" \
  -H "Authorization: Bearer ${API_KEY}" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "测试提示词"
  }' \
  -w "\nHTTP Status: %{http_code}\n\n"

echo "=== 测试完成 ==="
