#!/bin/bash

# 测试 Midjourney Motion Video 参数检测和自动补充功能

API_BASE_URL="http://localhost:3000"
API_KEY="your_api_key_here"

echo "=== 测试 Midjourney Motion Video 参数检测 ==="

# 测试1: 有 --motion high 但没有 --video 1，应该自动补充
echo "测试1: --motion high 自动补充 --video 1"
curl -X POST "${API_BASE_URL}/mj/submit/imagine" \
  -H "Authorization: Bearer ${API_KEY}" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "一只可爱的小猫在花园里玩耍 --motion high"
  }' \
  -w "\nHTTP Status: %{http_code}\n\n"

# 测试2: 有 --motion low 但没有 --video 1，应该自动补充
echo "测试2: --motion low 自动补充 --video 1"
curl -X POST "${API_BASE_URL}/mj/submit/imagine" \
  -H "Authorization: Bearer ${API_KEY}" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "美丽的日落景色 --motion low"
  }' \
  -w "\nHTTP Status: %{http_code}\n\n"

# 测试3: 已经有 --motion high --video 1，不应该重复添加
echo "测试3: 已有完整参数，不重复添加"
curl -X POST "${API_BASE_URL}/mj/submit/imagine" \
  -H "Authorization: Bearer ${API_KEY}" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "城市夜景 --motion high --video 1"
  }' \
  -w "\nHTTP Status: %{http_code}\n\n"

# 测试4: 已经有 --motion low --video 1，不应该重复添加
echo "测试4: 已有完整参数，不重复添加"
curl -X POST "${API_BASE_URL}/mj/submit/imagine" \
  -H "Authorization: Bearer ${API_KEY}" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "森林中的小溪 --motion low --video 1"
  }' \
  -w "\nHTTP Status: %{http_code}\n\n"

# 测试5: 没有 motion 参数，应该按普通图片计费
echo "测试5: 普通图片生成，无motion参数"
curl -X POST "${API_BASE_URL}/mj/submit/imagine" \
  -H "Authorization: Bearer ${API_KEY}" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "一幅美丽的风景画"
  }' \
  -w "\nHTTP Status: %{http_code}\n\n"

# 测试6: 只有 --video 1 但没有 motion，应该按普通图片计费
echo "测试6: 只有 --video 1，无motion参数"
curl -X POST "${API_BASE_URL}/mj/submit/imagine" \
  -H "Authorization: Bearer ${API_KEY}" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "现代建筑设计 --video 1"
  }' \
  -w "\nHTTP Status: %{http_code}\n\n"

# 测试7: 测试不同模式下的视频参数 (relax mode)
echo "测试7: relax模式下的motion参数"
curl -X POST "${API_BASE_URL}/mj-relax/submit/imagine" \
  -H "Authorization: Bearer ${API_KEY}" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "海浪拍打海岸 --motion high"
  }' \
  -w "\nHTTP Status: %{http_code}\n\n"

# 测试8: 测试turbo模式下的视频参数
echo "测试8: turbo模式下的motion参数"
curl -X POST "${API_BASE_URL}/mj-turbo/submit/imagine" \
  -H "Authorization: Bearer ${API_KEY}" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "飞鸟在天空中翱翔 --motion low"
  }' \
  -w "\nHTTP Status: %{http_code}\n\n"

echo "=== 测试完成 ==="

echo ""
echo "预期行为："
echo "1. 测试1和2应该自动在prompt后添加 --video 1"
echo "2. 测试3和4不应该重复添加参数"
echo "3. 测试5和6应该按普通图片价格计费，模型为 midjourney-fast"
echo "4. 测试7和8应该按对应模式的视频价格计费"
echo "5. 所有包含motion参数的请求都应该按视频模型计费，模型为 midjourney-video-{mode}"
echo "6. 检查数据库中任务的模型类型是否正确设置"

echo ""
echo "检查任务类型的方法："
echo "1. 查看日志中的模型名称"
echo "2. 查看数据库中 midjourneys 表的记录"
echo "3. 普通任务应该显示为 midjourney-fast/relax/turbo"
echo "4. 视频任务应该显示为 midjourney-video-fast/relax/turbo"
