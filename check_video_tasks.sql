-- 查询最近的 Midjourney 任务，检查视频任务的模型类型
-- 这个脚本可以用来验证 motion 参数检测是否正确工作

-- 查询最近 10 个任务的基本信息
SELECT 
    id,
    user_id,
    action,
    mode,
    prompt,
    submit_time,
    status,
    progress,
    quota,
    video_url,
    CASE 
        WHEN prompt LIKE '%--motion high%' OR prompt LIKE '%--motion low%' THEN 'VIDEO'
        ELSE 'IMAGE'
    END as task_type,
    CASE 
        WHEN prompt LIKE '%--motion high%' OR prompt LIKE '%--motion low%' THEN 
            CASE 
                WHEN mode = 'fast' THEN 'midjourney-video-fast'
                WHEN mode = 'relax' THEN 'midjourney-video-relax'  
                WHEN mode = 'turbo' THEN 'midjourney-video-turbo'
                ELSE CONCAT('midjourney-video-', COALESCE(mode, 'fast'))
            END
        ELSE 
            CASE 
                WHEN mode = 'fast' THEN 'midjourney-fast'
                WHEN mode = 'relax' THEN 'midjourney-relax'
                WHEN mode = 'turbo' THEN 'midjourney-turbo'
                ELSE CONCAT('midjourney-', COALESCE(mode, 'fast'))
            END
    END as expected_model
FROM midjourneys 
WHERE action = 'IMAGINE'
ORDER BY submit_time DESC 
LIMIT 10;

-- 查询包含 motion 参数的任务
SELECT 
    id,
    user_id,
    action,
    mode,
    prompt,
    submit_time,
    status,
    quota,
    video_url,
    CASE 
        WHEN prompt LIKE '%--video 1%' THEN 'HAS_VIDEO_PARAM'
        ELSE 'NO_VIDEO_PARAM'
    END as video_param_status
FROM midjourneys 
WHERE (prompt LIKE '%--motion high%' OR prompt LIKE '%--motion low%')
  AND action = 'IMAGINE'
ORDER BY submit_time DESC 
LIMIT 20;

-- 统计不同类型任务的数量和平均费用
SELECT 
    CASE 
        WHEN prompt LIKE '%--motion high%' OR prompt LIKE '%--motion low%' THEN 'VIDEO_TASK'
        ELSE 'IMAGE_TASK'
    END as task_category,
    mode,
    COUNT(*) as task_count,
    AVG(quota) as avg_quota,
    MIN(quota) as min_quota,
    MAX(quota) as max_quota,
    SUM(quota) as total_quota
FROM midjourneys 
WHERE action = 'IMAGINE'
  AND submit_time > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY)) * 1000
GROUP BY 
    CASE 
        WHEN prompt LIKE '%--motion high%' OR prompt LIKE '%--motion low%' THEN 'VIDEO_TASK'
        ELSE 'IMAGE_TASK'
    END,
    mode
ORDER BY task_category, mode;

-- 查询自动补充 --video 1 参数的任务
SELECT 
    id,
    user_id,
    prompt,
    submit_time,
    quota,
    CASE 
        WHEN prompt LIKE '%--motion high --video 1%' OR prompt LIKE '%--motion low --video 1%' THEN 'AUTO_ADDED'
        WHEN (prompt LIKE '%--motion high%' OR prompt LIKE '%--motion low%') AND prompt NOT LIKE '%--video 1%' THEN 'MISSING_VIDEO_PARAM'
        ELSE 'NORMAL'
    END as video_param_status
FROM midjourneys 
WHERE (prompt LIKE '%--motion high%' OR prompt LIKE '%--motion low%')
  AND action = 'IMAGINE'
ORDER BY submit_time DESC 
LIMIT 15;

-- 验证计费是否正确（视频任务应该比普通任务费用更高）
SELECT 
    'VIDEO_TASKS' as task_type,
    mode,
    COUNT(*) as count,
    AVG(quota) as avg_quota
FROM midjourneys 
WHERE (prompt LIKE '%--motion high%' OR prompt LIKE '%--motion low%')
  AND action = 'IMAGINE'
  AND submit_time > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 HOUR)) * 1000
GROUP BY mode

UNION ALL

SELECT 
    'IMAGE_TASKS' as task_type,
    mode,
    COUNT(*) as count,
    AVG(quota) as avg_quota
FROM midjourneys 
WHERE prompt NOT LIKE '%--motion high%' 
  AND prompt NOT LIKE '%--motion low%'
  AND action = 'IMAGINE'
  AND submit_time > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 HOUR)) * 1000
GROUP BY mode
ORDER BY task_type, mode;
