feat: implement Midjourney edits API compatibility layer

- Add support for /mj/submit/edits endpoint with two modes:
  * Edit mode: uses maskBase64 for selective image editing
  * Retexture mode: processes entire image transformation
- Add route detection for /submit/edits path
- Implement comprehensive parameter validation:
  * Require prompt for both modes
  * Support both image and base64 input fields
  * Validate image/mask requirements based on mode
- Add image processing logic:
  * Handle URL and base64 image formats
  * Process mask images for edit mode
  * Save base64 images as local files when needed
- Maintain backward compatibility with existing MJ API structure
- Support EDIT and RETEXTURE task types as per API specification

This implementation enables advanced image editing capabilities
compatible with Midjourney-Proxy edit interface requirements.