# Midjourney Motion Video 参数自动处理功能

## 功能概述

在 `/mj/submit/imagine` 接口中，系统会自动检测 prompt 中的 `--motion` 参数，并进行以下处理：

1. **自动补充参数**：如果检测到 `--motion high` 或 `--motion low` 但缺少 `--video 1`，系统会自动补充
2. **视频模型计费**：包含 motion 参数的请求将按视频模型价格计费
3. **智能检测**：避免重复添加已存在的参数

## 支持的参数组合

### 自动检测的参数
- `--motion high`
- `--motion low`

### 自动补充逻辑
- 如果 prompt 包含 `--motion high` 但没有 `--video 1` → 自动添加 `--video 1`
- 如果 prompt 包含 `--motion low` 但没有 `--video 1` → 自动添加 `--video 1`
- 如果已经包含 `--video 1` → 不重复添加

## 计费规则

### 视频模式计费（包含 motion 参数时）
- **Fast 模式**: `mj_video` = 5.0 积分
- **Relax 模式**: `mj_relax_video` = 4.0 积分  
- **Turbo 模式**: `mj_turbo_video` = 6.0 积分

### 普通模式计费（不包含 motion 参数时）
- **Fast 模式**: `mj_imagine` = 0.1 积分
- **Relax 模式**: `mj_relax_imagine` = 0.1 积分
- **Turbo 模式**: `mj_turbo_imagine` = 0.1 积分

## 使用示例

### 示例 1: 自动补充参数
**输入:**
```json
{
  "prompt": "一只可爱的小猫在花园里玩耍 --motion high"
}
```

**系统处理后:**
```json
{
  "prompt": "一只可爱的小猫在花园里玩耍 --motion high --video 1"
}
```
**计费**: 按视频模型计费 (5.0 积分)

### 示例 2: 已有完整参数
**输入:**
```json
{
  "prompt": "城市夜景 --motion low --video 1"
}
```

**系统处理后:**
```json
{
  "prompt": "城市夜景 --motion low --video 1"
}
```
**计费**: 按视频模型计费 (5.0 积分)

### 示例 3: 普通图片生成
**输入:**
```json
{
  "prompt": "一幅美丽的风景画"
}
```

**系统处理后:**
```json
{
  "prompt": "一幅美丽的风景画"
}
```
**计费**: 按普通模型计费 (0.1 积分)

## API 调用示例

### Fast 模式 (默认)
```bash
curl -X POST "http://localhost:3000/mj/submit/imagine" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "海浪拍打海岸 --motion high"
  }'
```

### Relax 模式
```bash
curl -X POST "http://localhost:3000/mj-relax/submit/imagine" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "飞鸟在天空中翱翔 --motion low"
  }'
```

### Turbo 模式
```bash
curl -X POST "http://localhost:3000/mj-turbo/submit/imagine" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "森林中的小溪 --motion high"
  }'
```

## 日志记录

系统会记录以下操作日志：

### 自动补充参数日志
```
检测到motion参数但缺少--video 1，自动补充，用户ID: 12345, 原prompt: 一只可爱的小猫在花园里玩耍 --motion high --video 1
```

### 视频模式计费日志
```
检测到视频motion参数，任务将按视频模型计费，用户ID: 12345
```

### 价格未定义日志
```
MJ Video 未定义的 action: video, 使用默认价格: 5.000000
```

## 技术实现细节

### 参数检测逻辑
```go
// 检测 --motion high 或 --motion low 参数
hasMotionHigh := strings.Contains(midjRequest.Prompt, "--motion high")
hasMotionLow := strings.Contains(midjRequest.Prompt, "--motion low")
hasVideo1 := strings.Contains(midjRequest.Prompt, "--video 1")

if hasMotionHigh || hasMotionLow {
    isVideoMode = true
    // 如果有 motion 参数但没有 --video 1，自动补充
    if !hasVideo1 {
        midjRequest.Prompt = midjRequest.Prompt + " --video 1"
    }
}
```

### 价格计算逻辑
```go
if isVideoMode {
    // 如果是视频模式，使用视频价格
    actionPrice, ok = modelFixedPriceMap[fmt.Sprintf("mj_%svideo", modeForPrice)]
    if !ok {
        actionPrice = 5.0 // 视频操作默认价格
    }
} else {
    // 普通模式，使用原有逻辑
    actionPrice, ok = modelFixedPriceMap[fmt.Sprintf("mj_%s%s", modeForPrice, strings.ToLower(action))]
}
```

## 注意事项

1. **仅限 IMAGINE 操作**: 此功能仅在 `action == "IMAGINE"` 时生效
2. **大小写敏感**: 参数检测区分大小写，必须是 `--motion high` 或 `--motion low`
3. **参数顺序**: 系统会在原 prompt 末尾添加 `--video 1`
4. **计费差异**: 视频模式计费比普通模式高 50 倍 (5.0 vs 0.1)
5. **模式支持**: 支持 fast、relax、turbo 三种模式的视频计费

## 错误处理

- 如果视频价格未配置，使用默认价格 5.0 积分
- 系统会记录所有参数处理和计费决策的日志
- 不会影响原有的普通图片生成功能

## 配置管理

视频价格可以通过修改 `relay/billing/ratio/model.go` 中的 `ModelFixedPrice` 配置：

```go
// video generation prices (for --motion parameters)
"mj_video":       5.0, // fast mode video
"mj_relax_video": 4.0, // relax mode video  
"mj_turbo_video": 6.0, // turbo mode video
```
