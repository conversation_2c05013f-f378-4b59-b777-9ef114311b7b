# Midjourney Edits API 完整文档

## 概述

Midjourney Edits API 提供了两种图片编辑功能：
1. **编辑模式（EDIT）**：使用遮罩图片对原图进行局部编辑
2. **转绘模式（RETEXTURE）**：对整张图片进行风格转换或重新绘制

## 接口信息

- **URL**: `/mj/submit/edits`
- **方法**: `POST`
- **Content-Type**: `application/json`
- **认证**: Bearer Token

## 请求参数

### 通用参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| prompt | string | 是 | 编辑提示词，描述想要的编辑效果 |

### 编辑模式参数（EDIT）

当提供 `maskBase64` 参数时，接口将进入编辑模式：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| maskBase64 | string | 是 | 遮罩图片的 base64 编码或 URL，透明区域表示需要编辑的部分 |
| image | string | 否 | 原图片 URL 或 base64 编码 |
| base64 | string | 否 | 原图片的 base64 编码（与 image 二选一） |

### 转绘模式参数（RETEXTURE）

当不提供 `maskBase64` 参数时，接口将进入转绘模式：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| image | string | 是 | 要转绘的图片 URL 或 base64 编码 |
| base64 | string | 否 | 图片的 base64 编码（与 image 二选一） |

## 请求示例

### 编辑模式示例

```json
{
  "prompt": "在天空中添加几朵白云",
  "image": "https://example.com/landscape.jpg",
  "maskBase64": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA4nEKtAAAAABJRU5ErkJggg=="
}
```

使用本地 base64 图片：

```json
{
  "prompt": "将选中区域改为蓝色天空",
  "base64": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...",
  "maskBase64": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA4nEKtAAAAABJRU5ErkJggg=="
}
```

### 转绘模式示例

```json
{
  "prompt": "转换为水彩画风格",
  "image": "https://example.com/photo.jpg"
}
```

使用 base64 图片：

```json
{
  "prompt": "转换为油画风格",
  "base64": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
}
```

## 响应格式

### 成功响应

```json
{
  "code": 1,
  "description": "提交成功",
  "result": "1234567890",
  "properties": {
    "discordInstanceId": "123456"
  }
}
```

### 错误响应

```json
{
  "code": 4,
  "description": "prompt_is_required",
  "properties": {}
}
```

## 错误码说明

| 错误码 | 描述 | 说明 |
|--------|------|------|
| 4 | prompt_is_required | 缺少必填的 prompt 参数 |
| 4 | image_or_maskBase64_is_required | 既没有提供图片也没有提供遮罩 |
| 4 | channel_not_found | 指定的渠道不存在 |
| 4 | save_image_failed | 图片保存失败 |
| 4 | invalid_base64_format | Base64 格式无效 |

## 使用说明

### 遮罩图片要求

- **格式**：PNG（必须支持透明度）
- **透明区域**：表示需要编辑的部分（Alpha = 0）
- **不透明区域**：表示保持不变的部分（Alpha > 0）
- **建议尺寸**：与原图保持一致的比例
- **颜色**：遮罩的颜色不重要，只有透明度有意义

### 图片格式支持

- **支持的格式**：JPEG、PNG、WebP、GIF
- **支持的输入方式**：
  - HTTP/HTTPS URL
  - Base64 编码（支持 data URI 格式：`data:image/jpeg;base64,xxx`）
  - 纯 Base64 字符串（自动检测格式）

### 提示词建议

- **编辑模式**：描述想要在遮罩区域添加或修改的内容
  - 例如："添加一只飞鸟"、"将天空改为夕阳"、"在草地上放一朵花"
- **转绘模式**：描述想要的整体风格或效果
  - 例如："转换为水彩画风格"、"变成卡通风格"、"改为黑白照片"

### 最佳实践

1. **遮罩制作**：
   - 使用 Photoshop、GIMP 等工具制作精确的遮罩
   - 确保遮罩边缘平滑，避免锯齿
   - 遮罩区域不要太小或太复杂

2. **图片质量**：
   - 原图分辨率建议在 512x512 到 2048x2048 之间
   - 避免过度压缩的图片
   - 确保图片清晰度足够

3. **提示词优化**：
   - 使用具体、清晰的描述
   - 避免过于复杂或矛盾的要求
   - 可以包含风格、颜色、光线等细节

## cURL 示例

### 编辑模式

```bash
curl -X POST "http://localhost:3000/mj/submit/edits" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "在天空中添加几朵白云",
    "image": "https://example.com/landscape.jpg",
    "maskBase64": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA4nEKtAAAAABJRU5ErkJggg=="
  }'
```

### 转绘模式

```bash
curl -X POST "http://localhost:3000/mj/submit/edits" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "转换为水彩画风格",
    "image": "https://example.com/photo.jpg"
  }'
```

### 使用本地文件测试

```bash
# 将图片转换为 base64
IMAGE_BASE64=$(base64 -i your_image.jpg)
MASK_BASE64=$(base64 -i your_mask.png)

curl -X POST "http://localhost:3000/mj/submit/edits" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d "{
    \"prompt\": \"在选中区域添加彩虹\",
    \"base64\": \"data:image/jpeg;base64,${IMAGE_BASE64}\",
    \"maskBase64\": \"data:image/png;base64,${MASK_BASE64}\"
  }"
```

## 技术实现细节

### 内部处理流程

1. **参数验证**：检查必填参数和参数格式
2. **模式识别**：根据是否有 `maskBase64` 参数判断编辑模式
3. **图片处理**：
   - URL 图片：直接传递给后端服务
   - Base64 图片：保存为临时文件，返回本地路径
4. **请求转发**：将处理后的请求转发给 Midjourney-Proxy 服务
5. **响应返回**：返回任务 ID 和相关信息

### 兼容性说明

- 兼容原有的 Midjourney API 格式
- 支持多种图片输入方式
- 自动处理 Base64 和 URL 格式转换
- 向后兼容现有的 `base64` 字段

## 常见问题

### Q: 遮罩图片应该如何制作？
A: 使用图像编辑软件（如 Photoshop）创建 PNG 格式的遮罩，需要编辑的区域设为透明，保持不变的区域设为不透明（任意颜色）。

### Q: 支持哪些图片格式？
A: 支持 JPEG、PNG、WebP、GIF 格式的图片，推荐使用 JPEG 或 PNG。

### Q: Base64 图片有大小限制吗？
A: 建议单张图片不超过 10MB，过大的图片可能导致处理超时。

### Q: 可以同时使用 image 和 base64 参数吗？
A: 可以，系统会优先使用 base64 参数，如果 base64 为空则使用 image 参数。

### Q: 转绘模式和编辑模式有什么区别？
A: 转绘模式会对整张图片进行重新绘制，编辑模式只会修改遮罩指定的区域，其他区域保持不变。

## 示例遮罩图片

遮罩图片示例可以参考：https://file.rixapi.com/mask.png

这个示例展示了如何制作遮罩：
- 透明区域（需要编辑的部分）
- 不透明区域（保持不变的部分）

## 总结

Midjourney Edits API 现已完全兼容参考文档中的接口规范，支持：

1. ✅ 编辑图片模式（EDIT 任务类型）
2. ✅ 转绘图片模式（RETEXTURE 任务类型）
3. ✅ 多种图片输入格式（URL、Base64）
4. ✅ 完整的参数验证和错误处理
5. ✅ 与现有 Midjourney API 的兼容性

接口已经可以正常使用，可以通过测试脚本进行验证。
